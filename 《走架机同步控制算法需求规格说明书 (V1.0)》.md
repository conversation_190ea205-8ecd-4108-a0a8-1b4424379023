# 《走架机同步控制算法需求规格说明书 (V1.0)》

### 《走架机同步控制算法需求规格说明书 (V1.0)》

#### 1.0 项目概述

本项目旨在为汇川（Inovance）控制系统开发一套应用层算法，用以复现西门子（Siemens）控制器中“齿轮同步（Gear Sync）”功能在特定工艺下的高级脱同步效果。核心目标是，通过软件算法计算出精确的控制切换点（“去同步拐点”），使得从动轴（罗拉）能够通过一系列独立的定位指令（`POS`命令），模拟出与主动轴（走车）复杂的、平滑的、多段式的牵伸同步关系。

#### 2.0 系统与环境

*   **目标硬件**: 汇川系列运动控制器/PLC。
    
*   **算法验证与仿真平台**: MATLAB R2018a或更高版本。
    
*   **依赖项**: 项目需基于客户提供的 `poscontrol_calculation.m` 函数进行开发，该函数用于计算主动轴的7段S型曲线。
    

#### 3.0 核心术语定义

*   **走车 (Master Axis)**: 系统中的主动轴，其执行一个完整的、单一的位置控制命令。
    
*   **罗拉 (Slave Axis)**: 系统中的从动轴，其运动需要精确跟随主动轴，并满足特定的牵伸比要求。
    
*   **牵伸比 (Tension Ratio)**: `走车速度 / 罗拉速度` 或 `走车行程 / 罗拉行程` 的比例。
    
*   **去同步拐点 (Desynchronization Turning Point)**: 一个**计算出的绝对位置坐标**。当罗拉的物理位置到达此坐标时，系统必须立即中断当前的同步跟随指令，并发出最终的独立减速定位指令。这是本项目的核心求解目标。
    

#### 4.0 功能需求

**4.1. 主动轴 (走车) 控制逻辑**

*   走车接收一个单一的`POS`指令，完成从起点到终点的全部运动。
    
*   其运动轨迹必须为标准的7段式、位置3次方S型曲线。
    

**4.2. 从动轴 (罗拉) 总体控制策略**

*   罗拉不使用控制器内置的`GEAR`指令进行全程同步。
    
*   罗拉的运动由**一个或多个首尾相连的独立**`**POS**`**指令**拼接而成。
    
*   每一个用于“模拟同步”的`POS`指令，其运动参数（最大速度、加速度、加加速度）都必须根据走车的对应参数和当前的牵伸比，进行**按比例计算**。
    
*   指令之间的切换点（即“去同步拐点”）由本项目的核心算法进行**离线或在线计算**。
    

**4.3. 从动轴 (罗拉) - 两段牵伸模式**

*   此模式下，罗拉的运动由**两个**`**POS**`**指令**构成。
    
*   **指令一 (模拟同步)**:
    
    *   该`POS`指令的目标是让罗拉的运动状态（速度、加速度）与走车按“分散牵伸比 `HMI_r64QianShen_FenSan`”保持同步。
        
    *   此指令的目标位置理论上设定为一个极远值或“去同步拐点”位置。
        
*   **指令二 (独立减速定位)**:
    
    *   **触发条件**: 罗拉的物理位置到达算法计算出的“去同步拐点”。
        
    *   **执行动作**: 立即中断指令一，并发出指令二。
        
    *   **指令参数**: 该`POS`指令使用罗拉自身的、预设的独立减速参数 (`HMI_r64_Gear_LuoLa...`)，其目标位置为罗拉的最终停止位置（根据总牵伸比`HMI_r64QianShen_All`计算得出）。
        
*   **关键逻辑**: 必须正确处理“去同步拐点”发生在走车减速阶段的情况。如果走车先于罗拉到达拐点而开始减速，罗拉必须**立即跟随走车进行按比例减速**，直到罗拉自身的位置到达“去同步拐点”，才能切换到指令二。
    

#### 5.0 算法核心要求

**5.1. “去同步拐点”求解算法 (针对两段牵伸)**

*   算法必须采用“**全轨迹反算**”逻辑，以应对拐点位置的动态不确定性。
    
*   **步骤 1**: 基于走车的完整运动曲线和分散牵伸比，生成一条**理想的、全程同步的**罗拉运动轨迹（包含与走车同步的减速段）。这条轨迹应包含每个时间点的理想位置和理想速度。
    
*   **步骤 2**: 从该理想轨迹的终点开始，**向前反向迭代**每一个数据点。
    
*   **步骤 3**: 在每一次迭代中，根据当前点的**理想速度**，调用独立的S曲线减速模型，计算出从该点完全停止所需的“**刹车距离**”。
    
*   **步骤 4**: 计算“**期望停止位置**” = `当前点的理想位置` + `刹车距离`。
    
*   **步骤 5**: 比较“期望停止位置”与罗拉的“最终目标位置”。找到使两者相等的那个点，该点即为最终的“去同步拐点”。
    

#### 6.0 输入与输出

*   **输入参数**: 算法需要接收所有在 `Test_PosControl_3Duan_QianShen.m` 文件中定义的HMI参数，包括但不限于：
    
    *   走车的目标位置、速度、加速度、加加速度。
        
    *   两段牵伸所需的总牵伸比、分散牵伸比。
        
    *   罗拉独立减速时的加速度、加加速度。
        
*   **输出结果**: 算法的核心输出为一个或多个“拐点”的详细数据，至少应包含：
    
    *   拐点的绝对位置坐标。
        
    *   罗拉在到达拐点时的瞬时速度。
        
    *   拐点发生的时刻。
        

#### 7.0 验收标准

1.  **算法仿真验收**:
    
    *   在MATLAB中运行求解脚本，程序不得崩溃，并能针对给定的HMI参数，计算出明确、合理的“去同步拐点”位置。
        
    *   生成的仿真曲线图必须平滑，无突变或不符合物理规律的跳变。
        
    *   仿真结束时，走车和罗拉必须精确地停在各自的目标位置，其总行程必须满足总牵伸比 `HMI_r64QianShen_All` 的要求。
        
2.  **最终实施验收**:
    
    *   算法移植到汇川控制器后，物理设备运行平稳，在“拐点”进行指令切换时无明显冲击或抖动。
        
    *   在多次重复运行后，走车和罗拉的最终停止位置精确、可重复。