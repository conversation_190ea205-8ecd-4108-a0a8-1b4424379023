%% S曲线算法测试脚本
clear; clc; close all;

% 测试参数
stroke = 4000.0;          % 走车行程 (mm)
max_speed = 600.0;        % 最大速度 (mm/s)
accel_pos = 300.0;        % 正向加速度 (mm/s²)
accel_neg = 800.0;        % 负向加速度 (mm/s²)
jerk = 600.0;             % 加加速度 (mm/s³)
Ts = 0.004;               % 采样时间 (s)

fprintf('S曲线算法测试\n');
fprintf('目标距离: %.1f mm\n', stroke);

% 调用S曲线生成函数
try
    [time, position, velocity] = generate_s_curve_fixed(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);
    
    fprintf('轨迹生成成功!\n');
    fprintf('总时长: %.3f s\n', time(end));
    fprintf('最终位置: %.6f mm\n', position(end));
    fprintf('位置误差: %.6f mm\n', abs(position(end) - stroke));
    
    % 绘制结果
    figure('Name', 'S曲线测试结果');
    
    subplot(3,1,1);
    plot(time, position, 'b-', 'LineWidth', 2);
    title('位置轨迹');
    xlabel('时间 (s)'); ylabel('位置 (mm)');
    grid on;
    
    subplot(3,1,2);
    plot(time, velocity, 'r-', 'LineWidth', 2);
    title('速度轨迹');
    xlabel('时间 (s)'); ylabel('速度 (mm/s)');
    grid on;
    
    subplot(3,1,3);
    acceleration = [0; diff(velocity)/Ts];
    plot(time, acceleration, 'g-', 'LineWidth', 2);
    title('加速度轨迹');
    xlabel('时间 (s)'); ylabel('加速度 (mm/s²)');
    grid on;
    
catch ME
    fprintf('错误: %s\n', ME.message);
end

function [time, position, velocity] = generate_s_curve_fixed(dist, v_max, a_accel, a_decel, j_max, Ts)
%% 修复版S曲线生成器

fprintf('使用修复版S曲线算法...\n');

% Step 1: 检查加加速度限制
a_accel_limited = a_accel;
if v_max * j_max < a_accel^2
    a_accel_limited = sqrt(v_max * j_max);
    fprintf('加速度受限: %.1f -> %.1f mm/s²\n', a_accel, a_accel_limited);
end

a_decel_limited = a_decel;
if v_max * j_max < a_decel^2
    a_decel_limited = sqrt(v_max * j_max);
    fprintf('减速度受限: %.1f -> %.1f mm/s²\n', a_decel, a_decel_limited);
end

% Step 2: 计算各阶段时长
dur_j1_a = a_accel_limited / j_max;
dur_a_const = max(0, v_max / a_accel_limited - dur_j1_a);

dur_j1_d = a_decel_limited / j_max;
dur_d_const = max(0, v_max / a_decel_limited - dur_j1_d);

% Step 3: 计算各段距离
% 加速段距离
if dur_a_const > 0
    % 梯形加速
    dist_a_total = 0.5 * a_accel_limited * dur_j1_a^2 + a_accel_limited * dur_j1_a * dur_a_const + 0.5 * a_accel_limited * dur_j1_a^2;
else
    % 纯S形加速
    dist_a_total = (1/3) * a_accel_limited * dur_j1_a^2;
end

% 减速段距离
if dur_d_const > 0
    % 梯形减速
    dist_d_total = 0.5 * a_decel_limited * dur_j1_d^2 + a_decel_limited * dur_j1_d * dur_d_const + 0.5 * a_decel_limited * dur_j1_d^2;
else
    % 纯S形减速
    dist_d_total = (1/3) * a_decel_limited * dur_j1_d^2;
end

% 恒速段距离
dist_v_const = dist - dist_a_total - dist_d_total;
dur_v_const = max(0, dist_v_const / v_max);

fprintf('距离分配: 加速%.1f + 恒速%.1f + 减速%.1f = %.1f mm\n', ...
    dist_a_total, dist_v_const, dist_d_total, dist_a_total + dist_v_const + dist_d_total);

% Step 4: 生成轨迹
T1 = dur_j1_a;
T2 = T1 + dur_a_const;
T3 = T2 + dur_j1_a;
T4 = T3 + dur_v_const;
T5 = T4 + dur_j1_d;
T6 = T5 + dur_d_const;
T7 = T6 + dur_j1_d;

time = (0:Ts:T7)';
N = length(time);
position = zeros(N,1);
velocity = zeros(N,1);
acceleration = zeros(N,1);

for i = 2:N
    t = time(i-1);
    jerk = 0;
    
    if t < T1
        jerk = j_max;
    elseif t >= T1 && t < T2
        jerk = 0;
    elseif t >= T2 && t < T3
        jerk = -j_max;
    elseif t >= T3 && t < T4
        jerk = 0;
    elseif t >= T4 && t < T5
        jerk = -j_max;
    elseif t >= T5 && t < T6
        jerk = 0;
    elseif t >= T6 && t < T7
        jerk = j_max;
    end
    
    acceleration(i) = acceleration(i-1) + jerk * Ts;
    if acceleration(i) > a_accel_limited
        acceleration(i) = a_accel_limited;
    end
    if acceleration(i) < -a_decel_limited
        acceleration(i) = -a_decel_limited;
    end
    
    velocity(i) = velocity(i-1) + acceleration(i-1) * Ts;
    position(i) = position(i-1) + (velocity(i-1) + velocity(i)) * 0.5 * Ts;
end

velocity(end) = 0;
end
