%% 走架细纱机牵伸工艺仿真器 - 正确的S曲线距离计算版本
% 修复：使用标准S曲线运动学公式进行距离计算

clear; clc; close all;

%% ==================== 工艺参数配置 ====================
fprintf('走架细纱机牵伸工艺仿真器 V1.0 (距离计算修复版)\n');
fprintf('==================================================\n');

% 走车参数
stroke = 4000.0;          % 走车行程 (mm)
max_speed = 600.0;        % 最大速度 (mm/s)
accel_pos = 300.0;        % 正向加速度 (mm/s²)
accel_neg = 800.0;        % 负向加速度 (mm/s²)
jerk = 600.0;             % 加加速度 (mm/s³)
Ts = 0.004;               % 采样时间 (s)

% 罗拉参数
slave_accel_brake = 2000.0;    % 独立刹车加速度 (mm/s²)
slave_jerk_brake = 12500.0;    % 独立刹车加加速度 (mm/s³)

% 牵伸参数
ratio_distributed = 1.2;      % 分散牵伸比
ratio_total = 1.5;            % 总牵伸比

fprintf('工艺参数配置:\n');
fprintf('  走车行程: %.1f mm\n', stroke);
fprintf('  分散牵伸比: %.1f\n', ratio_distributed);
fprintf('  总牵伸比: %.1f\n', ratio_total);

%% ==================== 第一步：生成走车轨迹 ====================
fprintf('\n第一步：生成走车主轴S曲线轨迹...\n');

[master_time, master_pos, master_vel] = generate_correct_s_curve(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);

fprintf('  走车轨迹生成完成\n');
fprintf('  总时长: %.3f s, 数据点: %d\n', master_time(end), length(master_time));
fprintf('  最终位置: %.6f mm (目标: %.1f mm)\n', master_pos(end), stroke);

position_error = abs(master_pos(end) - stroke);
if position_error < 0.1
    fprintf('  位置精度: 通过 (误差: %.6f mm)\n', position_error);
else
    fprintf('  位置精度: 失败 (误差: %.6f mm)\n', position_error);
    error('S曲线生成器精度不满足要求');
end

%% ==================== 第二步：计算去同步拐点 ====================
fprintf('\n第二步：计算去同步拐点位置...\n');

% 生成理想同步轨迹（分散牵伸比）
ideal_slave_pos = master_pos / ratio_distributed;
ideal_slave_vel = master_vel / ratio_distributed;

% 计算罗拉最终目标位置（总牵伸比）
slave_final_target = stroke / ratio_total;

% 寻找拐点
turning_point_found = false;
N = length(master_time);

for i = N:-1:1
    current_pos = ideal_slave_pos(i);
    current_vel = ideal_slave_vel(i);
    
    % 计算刹车距离
    brake_distance = calculate_brake_distance(current_vel, slave_accel_brake, slave_jerk_brake);
    
    % 期望停止位置
    expected_stop = current_pos + brake_distance;
    
    % 检查是否找到拐点
    if abs(expected_stop - slave_final_target) < 0.05
        turning_point_found = true;
        turning_point_index = i;
        turning_point_time = master_time(i);
        turning_point_pos = current_pos;
        turning_point_vel = current_vel;
        turning_point_brake_dist = brake_distance;
        turning_point_expected_stop = expected_stop;
        break;
    end
end

if turning_point_found
    fprintf('  拐点计算成功!\n');
    fprintf('  拐点位置: %.3f mm\n', turning_point_pos);
    fprintf('  拐点时刻: %.3f s\n', turning_point_time);
    fprintf('  拐点速度: %.3f mm/s\n', turning_point_vel);
    fprintf('  预计刹车距离: %.3f mm\n', turning_point_brake_dist);
else
    error('拐点计算失败');
end

%% ==================== 第三步：执行双段POS指令仿真 ====================
fprintf('\n第三步：执行罗拉双段POS指令仿真...\n');

% POS指令一：模拟同步（起点到拐点）
sync_indices = 1:turning_point_index;
pos1_time = master_time(sync_indices);
pos1_slave_pos = ideal_slave_pos(sync_indices);
pos1_slave_vel = ideal_slave_vel(sync_indices);

% POS指令二：独立减速定位（拐点到终点）
[pos2_time, pos2_slave_pos, pos2_slave_vel] = generate_brake_trajectory(...
    turning_point_vel, turning_point_pos, slave_accel_brake, slave_jerk_brake, Ts, turning_point_time);

% 拼接完整罗拉轨迹
complete_slave_time = [pos1_time; pos2_time(2:end)];
complete_slave_pos = [pos1_slave_pos; pos2_slave_pos(2:end)];
complete_slave_vel = [pos1_slave_vel; pos2_slave_vel(2:end)];

fprintf('  双段POS指令仿真完成\n');
fprintf('  同步段时长: %.3f s\n', pos1_time(end));
fprintf('  减速段时长: %.3f s\n', pos2_time(end) - pos2_time(1));

%% ==================== 第四步：工艺质量验证 ====================
fprintf('\n第四步：工艺质量验证...\n');

% 验证总牵伸比
actual_total_ratio = master_pos(end) / complete_slave_pos(end);
ratio_error = abs(actual_total_ratio - ratio_total);

% 验证分散牵伸比
distributed_ratio_actual = master_pos(turning_point_index) / complete_slave_pos(turning_point_index);
distributed_error = abs(distributed_ratio_actual - ratio_distributed);

% 验证位置精度
final_position_error = abs(turning_point_expected_stop - slave_final_target);

fprintf('  工艺质量验证结果:\n');
fprintf('  总牵伸比: %.6f (目标: %.1f)\n', actual_total_ratio, ratio_total);
fprintf('  分散牵伸比: %.6f (目标: %.1f)\n', distributed_ratio_actual, ratio_distributed);
fprintf('  位置精度: %.6f mm (要求: <0.1mm)\n', final_position_error);

% 验收判断
ratio_pass = ratio_error < 0.01;
distributed_pass = distributed_error < 0.01;
position_pass = final_position_error < 0.1;
overall_pass = ratio_pass && distributed_pass && position_pass;

%% ==================== 生成仿真报告 ====================
fprintf('\n生成仿真报告...\n');

figure('Name', '走架细纱机牵伸工艺仿真报告', 'Position', [50, 50, 1200, 800]);

% 子图1: 位置轨迹
subplot(2,3,1);
plot(master_time, master_pos, 'b-', 'LineWidth', 2);
hold on;
plot(complete_slave_time, complete_slave_pos, 'r-', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [0, max(master_pos)], 'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('位置轨迹对比');
xlabel('时间 (s)'); ylabel('位置 (mm)');
legend('走车', '罗拉', '拐点', 'Location', 'best');
grid on;

% 子图2: 速度轨迹
subplot(2,3,2);
plot(master_time, master_vel, 'b-', 'LineWidth', 2);
hold on;
plot(complete_slave_time, complete_slave_vel, 'r-', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [0, max(master_vel)], 'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('速度轨迹对比');
xlabel('时间 (s)'); ylabel('速度 (mm/s)');
legend('走车', '罗拉', '拐点', 'Location', 'best');
grid on;

% 子图3: 实时牵伸比
subplot(2,3,3);
master_pos_interp = interp1(master_time, master_pos, complete_slave_time, 'linear', 'extrap');
realtime_ratio = master_pos_interp ./ complete_slave_pos;
plot(complete_slave_time, realtime_ratio, 'k-', 'LineWidth', 2);
hold on;
line([0, complete_slave_time(end)], [ratio_distributed, ratio_distributed], 'Color', 'r', 'LineStyle', '--', 'LineWidth', 2);
line([0, complete_slave_time(end)], [ratio_total, ratio_total], 'Color', 'b', 'LineStyle', '--', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [1, 2], 'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('实时牵伸比变化');
xlabel('时间 (s)'); ylabel('牵伸比');
legend('实时比', '分散牵伸比', '总牵伸比', '拐点', 'Location', 'best');
grid on;

% 其他子图...
subplot(2,3,4);
param_text = {
    ['走车行程: ' num2str(stroke) ' mm']
    ['罗拉行程: ' num2str(complete_slave_pos(end), '%.1f') ' mm']
    ['实际总比: ' num2str(actual_total_ratio, '%.6f')]
    ['位置误差: ' num2str(final_position_error, '%.6f') ' mm']
};
text(0.1, 0.5, param_text, 'FontSize', 10, 'VerticalAlignment', 'middle');
axis off; title('关键参数');

sgtitle('走架细纱机牵伸工艺仿真报告 (距离修复版)', 'FontSize', 14, 'FontWeight', 'bold');

%% ==================== 总结 ====================
fprintf('\n==================================================\n');
fprintf('走架细纱机牵伸工艺仿真完成!\n');

if overall_pass
    fprintf('工艺验收: 通过\n');
    fprintf('可用于生产指导\n');
else
    fprintf('工艺验收: 需要优化\n');
end

fprintf('==================================================\n');

%% ==================== 修复版S曲线生成器 ====================

function [time, position, velocity] = generate_correct_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
%% 修复版S曲线生成器 - 使用正确的运动学公式

fprintf('  使用修复版S曲线算法...\n');

% Step 1: 检查加加速度限制
a_accel_limited = a_accel;
if v_max * j_max < a_accel^2
    a_accel_limited = sqrt(v_max * j_max);
    fprintf('    加速度受限: %.1f -> %.1f mm/s²\n', a_accel, a_accel_limited);
end

a_decel_limited = a_decel;
if v_max * j_max < a_decel^2
    a_decel_limited = sqrt(v_max * j_max);
    fprintf('    减速度受限: %.1f -> %.1f mm/s²\n', a_decel, a_decel_limited);
end

% Step 2: 计算各阶段时长
dur_j1_a = a_accel_limited / j_max;
if v_max < a_accel_limited * dur_j1_a
    dur_j1_a = sqrt(v_max / j_max);
end
dur_a_const = v_max / a_accel_limited - dur_j1_a;
if dur_a_const < 0
    dur_a_const = 0;
end

dur_j1_d = a_decel_limited / j_max;
if v_max < a_decel_limited * dur_j1_d
    dur_j1_d = sqrt(v_max / j_max);
end
dur_d_const = v_max / a_decel_limited - dur_j1_d;
if dur_d_const < 0
    dur_d_const = 0;
end

% Step 3: 使用正确的S曲线距离公式
% 加速段距离（修正版公式）
if dur_a_const > 0
    % 梯形加速：S = v_max * (t_j + t_const) - 0.5 * a_max * t_j
    dist_a_total = v_max * (dur_j1_a + dur_a_const) - 0.5 * a_accel_limited * dur_j1_a;
else
    % 纯S形加速：S = (2/3) * v_max * t_j
    dist_a_total = (2/3) * v_max * dur_j1_a;
end

% 减速段距离（修正版公式）
if dur_d_const > 0
    % 梯形减速：S = v_max * (t_j + t_const) - 0.5 * a_max * t_j
    dist_d_total = v_max * (dur_j1_d + dur_d_const) - 0.5 * a_decel_limited * dur_j1_d;
else
    % 纯S形减速：S = (2/3) * v_max * t_j
    dist_d_total = (2/3) * v_max * dur_j1_d;
end

% 恒速段距离
dist_v_const = dist - dist_a_total - dist_d_total;
dur_v_const = dist_v_const / v_max;
if dur_v_const < 0
    dur_v_const = 0;
    % 重新计算不能达到最大速度的情况
    v_max_actual = sqrt(dist * j_max / 2);
    if v_max_actual > v_max
        v_max_actual = v_max;
    end
    % 重新计算各段时间和距离
    dur_j1_a = sqrt(v_max_actual / j_max);
    dur_j1_d = dur_j1_a;
    dur_a_const = 0;
    dur_d_const = 0;
    dist_a_total = (2/3) * v_max_actual * dur_j1_a;
    dist_d_total = dist_a_total;
    dist_v_const = 0;
    dur_v_const = 0;
    v_max = v_max_actual;
end

fprintf('    距离分配: 加速%.1f + 恒速%.1f + 减速%.1f = %.1f mm\n', ...
    dist_a_total, dist_v_const, dist_d_total, dist_a_total + dist_v_const + dist_d_total);

% Step 4: 生成轨迹
T1 = dur_j1_a;
T2 = T1 + dur_a_const;
T3 = T2 + dur_j1_a;
T4 = T3 + dur_v_const;
T5 = T4 + dur_j1_d;
T6 = T5 + dur_d_const;
T7 = T6 + dur_j1_d;

time = (0:Ts:T7)';
N = length(time);
position = zeros(N,1);
velocity = zeros(N,1);
acceleration = zeros(N,1);

for i = 2:N
    t = time(i-1);
    jerk = 0;
    
    if t < T1
        jerk = j_max;
    elseif t >= T1 && t < T2
        jerk = 0;
    elseif t >= T2 && t < T3
        jerk = -j_max;
    elseif t >= T3 && t < T4
        jerk = 0;
    elseif t >= T4 && t < T5
        jerk = -j_max;
    elseif t >= T5 && t < T6
        jerk = 0;
    elseif t >= T6 && t < T7
        jerk = j_max;
    end
    
    acceleration(i) = acceleration(i-1) + jerk * Ts;
    if acceleration(i) > a_accel_limited
        acceleration(i) = a_accel_limited;
    end
    if acceleration(i) < -a_decel_limited
        acceleration(i) = -a_decel_limited;
    end
    
    velocity(i) = velocity(i-1) + acceleration(i-1) * Ts;
    position(i) = position(i-1) + (velocity(i-1) + velocity(i)) * 0.5 * Ts;
end

velocity(end) = 0;
end

function brake_distance = calculate_brake_distance(v0, a_decel, j_max)
%% 计算刹车距离

if v0 < 0.001
    brake_distance = 0;
    return;
end

t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    % 纯S形减速
    t_j = sqrt(v0/j_max);
    brake_distance = v0 * t_j;  % 正确的纯S形减速距离公式
else
    % 梯形减速
    t_const = v0/a_decel - t_j;
    brake_distance = v0 * (t_j + t_const + t_j/2);  % 正确的梯形减速距离公式
end
end

function [time, position, velocity] = generate_brake_trajectory(v0, pos0, a_decel, j_max, Ts, t_start)
%% 生成独立减速轨迹

if v0 < 0.001
    time = t_start;
    position = pos0;
    velocity = 0;
    return;
end

t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    t_j = sqrt(v0/j_max);
    t_const = 0;
    T_total = 2 * t_j;
else
    t_const = v0/a_decel - t_j;
    T_total = 2 * t_j + t_const;
end

T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

time = (0:Ts:T_total)' + t_start;
N = length(time);

velocity = zeros(N,1);
accel = zeros(N,1);
position = zeros(N,1);
velocity(1) = v0;
position(1) = pos0;

for i = 2:N
    t_rel = time(i) - t_start;
    jerk = 0;
    
    if t_rel < T1
        jerk = -j_max;
    elseif t_rel >= T1 && t_rel < T2
        jerk = 0;
    elseif t_rel >= T2 && t_rel < T3
        jerk = j_max;
    end
    
    accel(i) = accel(i-1) + jerk * Ts;
    if accel(i) < -a_decel
        accel(i) = -a_decel;
    end
    
    velocity(i) = velocity(i-1) + accel(i-1) * Ts;
    position(i) = position(i-1) + (velocity(i-1) + velocity(i)) * 0.5 * Ts;
end

velocity(velocity < 0) = 0;
velocity(end) = 0;
end