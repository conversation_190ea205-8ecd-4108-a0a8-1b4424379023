%% S曲线"黄金标准"基准模块测试脚本
% 目标：验证S曲线轨迹生成器的精度和稳定性
% 遵循方法论V2.0的"单一事实来源"原则

clear; clc; close all;

fprintf('=== S曲线"黄金标准"基准模块测试 ===\n');

%% 测试用例定义
test_cases = [
    % [距离, 最大速度, 加速度, 减速度, 加加速度]
    4000, 600, 300, 800, 600;
    1000, 500, 400, 600, 800;
    500,  200, 100, 200, 300;
    2000, 800, 200, 400, 500;
];

test_descriptions = {
    '项目标准工况';
    '短距离高速';
    '低速工况';
    '长距离中速';
};

Ts = 0.004; % 采样时间
all_tests_passed = true;

%% 执行测试
for i = 1:size(test_cases, 1)
    dist = test_cases(i, 1);
    v_max = test_cases(i, 2);
    a_accel = test_cases(i, 3);
    a_decel = test_cases(i, 4);
    j_max = test_cases(i, 5);
    description = test_descriptions{i};
    
    fprintf('\n--- 测试用例 %d: %s ---\n', i, description);
    fprintf('参数: 距离=%.0fmm, 速度=%.0fmm/s, 加速=%.0fmm/s², 减速=%.0fmm/s², 加加速=%.0fmm/s³\n', ...
        dist, v_max, a_accel, a_decel, j_max);
    
    try
        % 调用S曲线生成器
        [time, position, velocity] = generate_s_curve_golden(dist, v_max, a_accel, a_decel, j_max, Ts);
        
        % 验证结果
        final_pos = position(end);
        final_vel = velocity(end);
        max_vel_actual = max(velocity);
        position_error = abs(final_pos - dist);
        
        fprintf('结果: 最终位置=%.6fmm, 位置误差=%.6fmm, 最终速度=%.6fmm/s\n', ...
            final_pos, position_error, final_vel);
        fprintf('实际最大速度=%.3fmm/s (设定=%.0fmm/s)\n', max_vel_actual, v_max);
        
        % 验收标准
        pos_pass = position_error < 0.1;
        vel_pass = abs(final_vel) < 0.001;
        smooth_pass = check_smoothness(velocity, Ts);
        
        if pos_pass && vel_pass && smooth_pass
            fprintf('✅ 测试通过!\n');
        else
            fprintf('❌ 测试失败!\n');
            if ~pos_pass; fprintf('  - 位置精度不达标\n'); end
            if ~vel_pass; fprintf('  - 最终速度不为零\n'); end
            if ~smooth_pass; fprintf('  - 轨迹不平滑\n'); end
            all_tests_passed = false;
        end
        
    catch ME
        fprintf('❌ 测试崩溃: %s\n', ME.message);
        all_tests_passed = false;
    end
end

%% 总结
fprintf('\n=== 测试总结 ===\n');
if all_tests_passed
    fprintf('🎉 所有测试用例通过! S曲线"黄金标准"基准模块验证成功!\n');
    fprintf('✅ 第一个"黄金标准"基准模块已就绪\n');
else
    fprintf('⚠️  存在失败的测试用例，需要修复\n');
end

%% 黄金标准S曲线生成器
function [time, position, velocity] = generate_s_curve_golden(dist, v_max, a_accel, a_decel, j_max, Ts)
%% 黄金标准S曲线轨迹生成器
% 严格按照物理模型，确保高精度和稳定性

% 输入验证
if dist <= 0 || v_max <= 0 || a_accel <= 0 || a_decel <= 0 || j_max <= 0 || Ts <= 0
    error('所有输入参数必须为正数');
end

% Step 1: 检查加加速度限制
a_accel_limited = min(a_accel, sqrt(v_max * j_max));
a_decel_limited = min(a_decel, sqrt(v_max * j_max));

% Step 2: 计算各阶段时长
% 加速段
t_j1 = a_accel_limited / j_max;
t_a = max(0, v_max / a_accel_limited - t_j1);

% 减速段  
t_j2 = a_decel_limited / j_max;
t_d = max(0, v_max / a_decel_limited - t_j2);

% Step 3: 计算各段距离（使用精确的物理公式）
% 加速段距离
if t_a > 0
    % 梯形加速：有恒加速段
    s_accel = a_accel_limited * t_j1^2 + a_accel_limited * t_j1 * t_a;
else
    % 三角形加速：纯S形
    s_accel = (2/3) * a_accel_limited * t_j1^2;
end

% 减速段距离
if t_d > 0
    % 梯形减速：有恒减速段
    s_decel = a_decel_limited * t_j2^2 + a_decel_limited * t_j2 * t_d;
else
    % 三角形减速：纯S形
    s_decel = (2/3) * a_decel_limited * t_j2^2;
end

% 恒速段距离和时间
s_const = max(0, dist - s_accel - s_decel);
t_v = s_const / v_max;

% Step 4: 计算时间节点
T1 = t_j1;                    % 加速度增加结束
T2 = T1 + t_a;               % 恒加速结束
T3 = T2 + t_j1;              % 加速度减少结束
T4 = T3 + t_v;               % 恒速结束
T5 = T4 + t_j2;              % 减速度增加结束
T6 = T5 + t_d;               % 恒减速结束
T7 = T6 + t_j2;              % 减速度减少结束

% Step 5: 生成时间序列
time = (0:Ts:T7)';
N = length(time);

% 初始化输出数组
position = zeros(N, 1);
velocity = zeros(N, 1);
acceleration = zeros(N, 1);

% Step 6: 逐点计算轨迹
for i = 2:N
    t = time(i-1);
    
    % 确定当前阶段的加加速度
    if t < T1
        jerk = j_max;                    % 阶段1：加速度增加
    elseif t < T2
        jerk = 0;                        % 阶段2：恒加速
    elseif t < T3
        jerk = -j_max;                   % 阶段3：加速度减少
    elseif t < T4
        jerk = 0;                        % 阶段4：恒速
    elseif t < T5
        jerk = -j_max;                   % 阶段5：减速度增加
    elseif t < T6
        jerk = 0;                        % 阶段6：恒减速
    elseif t < T7
        jerk = j_max;                    % 阶段7：减速度减少
    else
        jerk = 0;                        % 结束
    end
    
    % 数值积分更新状态
    acceleration(i) = acceleration(i-1) + jerk * Ts;
    
    % 限制加速度范围
    acceleration(i) = max(-a_decel_limited, min(a_accel_limited, acceleration(i)));
    
    % 更新速度和位置
    velocity(i) = velocity(i-1) + acceleration(i-1) * Ts;
    position(i) = position(i-1) + (velocity(i-1) + velocity(i)) * 0.5 * Ts;
end

% 确保最终状态
velocity(end) = 0;
acceleration(end) = 0;

end

%% 平滑性检查函数
function is_smooth = check_smoothness(velocity, Ts)
%% 检查速度曲线的平滑性
if length(velocity) < 3
    is_smooth = true;
    return;
end

% 计算加速度
accel = diff(velocity) / Ts;

% 计算加加速度
jerk = diff(accel) / Ts;

% 检查是否有异常跳变
max_jerk_change = max(abs(diff(jerk)));
is_smooth = max_jerk_change < 1000; % 允许的最大加加速度变化

end
