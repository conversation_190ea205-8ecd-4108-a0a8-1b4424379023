%% --- 刹车相关模块单元测试脚本 (决定版) ---
% 任务：最终验证刹车距离计算器与减速曲线生成器的自洽性
% 修正：重构两个核心函数，使其基于独立且正确的物理模型，并保证绝对自洽。

clear; clc; close all;

%% ==================== 测试参数设定 ====================
HMI_r64_Gear_LuoLa_negativeaccel = 2000.0; 
HMI_r64_Gear_LuoLa_jerk = 12500.0;         
Ts = 0.004;

% --- 定义不同速度的测试用例 ---
test_velocities = [521.74, 300, 100]; % 对应高速(梯形)、中速(梯形)、低速(三角形)

%% ==================== 执行测试 ====================
all_tests_passed = true;
for i = 1:length(test_velocities)
    v0 = test_velocities(i);
    fprintf('--- 运行测试用例 %d: 初始速度 v0 = %.2f mm/s ---\n', i, v0);
    
    % 1. 使用"预测"模块，计算理论刹车距离
    predicted_distance = calculate_braking_distance(v0, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
    
    % 2. 使用"执行"模块，生成实际减速曲线
    [time_vec, vel_vec] = generate_decel_profile(v0, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
    
    % 3. 对生成的实际速度曲线进行积分，得到实际刹车距离
    actual_distance = trapz(time_vec, vel_vec);
    
    % 4. 对比两个距离，验证自洽性
    error_val = abs(predicted_distance - actual_distance);
    
    fprintf('  预测距离 (来自函数A): %.4f mm\n', predicted_distance);
    fprintf('  实际距离 (来自函数B): %.4f mm\n', actual_distance);
    fprintf('  绝对误差: %.g mm\n', error_val);
    
    if error_val < 1e-9 % 允许极小的浮点计算误差
        fprintf('  结论: 测试通过! 模型完全自洽。\n\n');
    else
        fprintf('  结论: 测试失败! 模型不一致。\n\n');
        all_tests_passed = false;
    end
end

if all_tests_passed
    fprintf('============================================\n');
    fprintf('所有刹车模块测试用例均已通过！\n');
    fprintf('第二和第三个"黄金标准"基准模块已验证成功。\n');
    fprintf('============================================\n');
end

%% ==================== "黄金标准"基准模块 (决定版) ====================

function D_decel = calculate_braking_distance(v0, a_decel, j_max, Ts)
    % "黄金标准"模块 V2: 刹车距离计算器 (最终自洽版)
    % 完全依赖 generate_decel_profile 的结果
    if v0 < 0.001; D_decel = 0; return; end
    
    [time_vec, vel_vec] = generate_decel_profile(v0, a_decel, j_max, Ts);
    
    % 使用梯形积分法则，确保与验证方法一致
    D_decel = trapz(time_vec, vel_vec);
end


function [time_vec, vel_vec] = generate_decel_profile(v0, a_decel, j_max, Ts)
    % "黄金标准"模块 V3: 独立减速曲线生成器 (最终重构版)
    if v0 < 0.001; time_vec=0; vel_vec=0; return; end

    % --- Step 1: 精确计算总减速时间
    t_j = a_decel / j_max; % 达到最大减速度的加加速时间
    
    % 判断是梯形减速还是三角形减速
    if v0 * j_max < a_decel^2 
        % 三角形减速模式 (无法达到最大减速度)
        t_j = sqrt(v0/j_max);
        t_const = 0;
        T_total = 2 * t_j;
    else 
        % 梯形减速模式
        t_const = v0/a_decel - t_j;
        T_total = 2 * t_j + t_const;
    end
    
    % --- Step 2: 计算各阶段结束时刻点
    T1 = t_j;
    T2 = T1 + t_const;
    T3 = T2 + t_j;
    
    time_vec = (0:Ts:T_total)';
    N = length(time_vec);
    
    vel_vec = zeros(N,1);
    accel_vec = zeros(N,1);
    vel_vec(1) = v0;

    % --- Step 3: 状态积分
    for i = 2:N
        t = time_vec(i-1);
        jerk = 0;
        
        if t < T1
            jerk = -j_max;      % 加减速段
        elseif t >= T1 && t < T2
            jerk = 0;           % 匀减速段
        elseif t >= T2 && t < T3
            jerk = j_max;      % 减减速段
        end
        
        accel_vec(i) = accel_vec(i-1) + jerk * Ts;
        % 限制最大减速度
        if accel_vec(i) < -a_decel
            accel_vec(i) = -a_decel;
        end
        
        vel_vec(i) = vel_vec(i-1) + accel_vec(i-1) * Ts;
    end
    
    % 修正终点
    vel_vec(vel_vec < 0) = 0;
    vel_vec(end) = 0;
end