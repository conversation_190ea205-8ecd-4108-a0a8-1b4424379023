%% 简化S曲线测试
clear; clc;

fprintf('简化S曲线测试\n');

% 项目标准参数
dist = 4000;
v_max = 600;
a_accel = 300;
a_decel = 800;
j_max = 600;
Ts = 0.004;

try
    [time, pos, vel] = generate_s_curve_simple(dist, v_max, a_accel, a_decel, j_max, Ts);
    
    fprintf('生成成功!\n');
    fprintf('总时长: %.3f s\n', time(end));
    fprintf('最终位置: %.6f mm (目标: %.0f mm)\n', pos(end), dist);
    fprintf('位置误差: %.6f mm\n', abs(pos(end) - dist));
    fprintf('最终速度: %.6f mm/s\n', vel(end));
    
    if abs(pos(end) - dist) < 0.1 && abs(vel(end)) < 0.001
        fprintf('✅ 测试通过!\n');
    else
        fprintf('❌ 测试失败!\n');
    end
    
catch ME
    fprintf('错误: %s\n', ME.message);
end

function [time, position, velocity] = generate_s_curve_simple(dist, v_max, a_accel, a_decel, j_max, Ts)
%% 简化版S曲线生成器

% 限制加速度
a_accel = min(a_accel, sqrt(v_max * j_max));
a_decel = min(a_decel, sqrt(v_max * j_max));

% 计算时间参数
t_j1 = a_accel / j_max;
t_j2 = a_decel / j_max;

% 检查是否能达到最大速度
v_reach_accel = a_accel * t_j1;
v_reach_decel = a_decel * t_j2;

if v_reach_accel + v_reach_decel <= v_max
    % 能达到最大速度
    t_a = (v_max - v_reach_accel) / a_accel;
    t_d = (v_max - v_reach_decel) / a_decel;
    
    % 计算距离
    s_accel = v_reach_accel * t_j1 + v_reach_accel * t_a + 0.5 * a_accel * t_a^2;
    s_decel = v_reach_decel * t_j2 + v_reach_decel * t_d + 0.5 * a_decel * t_d^2;
    s_const = dist - s_accel - s_decel;
    t_v = s_const / v_max;
    
    if t_v < 0
        t_v = 0;
        s_const = 0;
    end
else
    % 不能达到最大速度，三角形轮廓
    t_a = 0;
    t_d = 0;
    t_v = 0;
    s_const = 0;
    
    % 重新计算
    v_actual = sqrt(dist * j_max / 2);
    if v_actual > v_max
        v_actual = v_max;
    end
    
    t_j1 = sqrt(v_actual / j_max);
    t_j2 = t_j1;
    a_accel = j_max * t_j1;
    a_decel = a_accel;
end

% 时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

% 生成轨迹
time = (0:Ts:T7)';
N = length(time);
position = zeros(N, 1);
velocity = zeros(N, 1);
acceleration = zeros(N, 1);

for i = 2:N
    t = time(i-1);
    jerk = 0;
    
    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    end
    
    acceleration(i) = acceleration(i-1) + jerk * Ts;
    velocity(i) = velocity(i-1) + acceleration(i-1) * Ts;
    position(i) = position(i-1) + velocity(i-1) * Ts + 0.5 * acceleration(i-1) * Ts^2;
end

% 确保最终状态
velocity(end) = 0;
acceleration(end) = 0;

end
