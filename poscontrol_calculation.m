function [T_total, V_max, A_max, J_max, phase_times, phase_positions, phase_velocities, ...
          t01, jerk01, a01, v01, s01, t12, jerk12, a12, v12, s12, ...
          t23, jerk23, a23, v23, s23, t34, jerk34, a34, v34, s34, ...
          t45, jerk45, a45, v45, s45, t56, jerk56, a56, v56, s56, ...
          t67, jerk67, a67, v67, s67] = ...
    poscontrol_calculation(position_set, velocity_set, positive_accel_set, negative_accel_set, ...
                          jerk_accel_set, jerk_decel_set, jerk_pos_set, jerk_neg_set)
%% S曲线位置控制计算函数
% 输入参数:
%   position_set - 目标位置 (mm)
%   velocity_set - 最大速度 (mm/s)
%   positive_accel_set - 正向加速度 (mm/s²)
%   negative_accel_set - 负向加速度 (mm/s²)
%   jerk_accel_set - 加速段躁动 (mm/s³)
%   jerk_decel_set - 减速段躁动 (mm/s³)
%   jerk_pos_set - 正向躁动 (mm/s³)
%   jerk_neg_set - 负向躁动 (mm/s³)

% 使用统一的躁动值
J = jerk_accel_set;
a_max = positive_accel_set;
a_min = -negative_accel_set;
v_max = velocity_set;
s_target = position_set;

% 计算S曲线的7个阶段
% 阶段1: 加速度增加 (0 -> a_max)
t_j1 = a_max / J;
% 阶段2: 恒定加速度
% 阶段3: 加速度减少 (a_max -> 0)
t_j2 = t_j1;
% 阶段4: 恒定速度
% 阶段5: 加速度减少 (0 -> a_min)
t_j3 = abs(a_min) / J;
% 阶段6: 恒定减速度
% 阶段7: 加速度增加 (a_min -> 0)
t_j4 = t_j3;

% 检查是否能达到最大速度
v_reach_accel = a_max * t_j1; % 加速段能达到的速度
v_reach_decel = abs(a_min) * t_j3; % 减速段需要的速度

% 计算到达最大加速度时的位移
s_j1 = (1/6) * J * t_j1^3;
s_j3 = (1/6) * J * t_j3^3;

% 简化计算：假设能达到最大速度
t_a = (v_max - v_reach_accel) / a_max; % 恒定加速时间
t_d = (v_max - v_reach_decel) / abs(a_min); % 恒定减速时间

% 计算各阶段位移
s_accel_total = s_j1 + v_reach_accel * t_a + a_max * t_a^2 / 2 + s_j1;
s_decel_total = s_j3 + v_reach_decel * t_d + abs(a_min) * t_d^2 / 2 + s_j3;
s_const_vel = s_target - s_accel_total - s_decel_total;

% 恒速时间
t_v = s_const_vel / v_max;

% 如果恒速时间为负，需要重新计算
if t_v < 0
    t_v = 0;
    % 重新计算不能达到最大速度的情况
    % 这里简化处理，假设能达到设定速度的一定比例
    v_actual = v_max * 0.8;
    t_a = (v_actual - v_reach_accel) / a_max;
    t_d = (v_actual - v_reach_decel) / abs(a_min);
    if t_a < 0; t_a = 0; end
    if t_d < 0; t_d = 0; end
end

% 生成时间向量和对应的运动参数
dt = 0.01; % 采样时间 10ms

% 阶段1: 加速度增加
t01 = 0:dt:t_j1;
jerk01 = J * ones(size(t01));
a01 = J * t01;
v01 = 0.5 * J * t01.^2;
s01 = (1/6) * J * t01.^3;

% 阶段2: 恒定加速度
t12 = (t_j1+dt):dt:(t_j1+t_a);
if isempty(t12); t12 = t_j1; end
jerk12 = zeros(size(t12));
a12 = a_max * ones(size(t12));
v12 = v_reach_accel + a_max * (t12 - t_j1);
s12 = s01(end) + v_reach_accel * (t12 - t_j1) + 0.5 * a_max * (t12 - t_j1).^2;

% 阶段3: 加速度减少
t_start_3 = t_j1 + t_a;
t23 = (t_start_3+dt):dt:(t_start_3+t_j2);
if isempty(t23); t23 = t_start_3; end
jerk23 = -J * ones(size(t23));
a23 = a_max - J * (t23 - t_start_3);
v_start_3 = v_reach_accel + a_max * t_a;
v23 = v_start_3 + a_max * (t23 - t_start_3) - 0.5 * J * (t23 - t_start_3).^2;
s_start_3 = s01(end) + v_reach_accel * t_a + 0.5 * a_max * t_a^2;
s23 = s_start_3 + v_start_3 * (t23 - t_start_3) + 0.5 * a_max * (t23 - t_start_3).^2 - (1/6) * J * (t23 - t_start_3).^3;

% 阶段4: 恒定速度
t_start_4 = t_start_3 + t_j2;
t34 = (t_start_4+dt):dt:(t_start_4+t_v);
if isempty(t34); t34 = t_start_4; end
jerk34 = zeros(size(t34));
a34 = zeros(size(t34));
v_const = v_start_3 + a_max * t_j2 - 0.5 * J * t_j2^2;
v34 = v_const * ones(size(t34));
s_start_4 = s_start_3 + v_start_3 * t_j2 + 0.5 * a_max * t_j2^2 - (1/6) * J * t_j2^3;
s34 = s_start_4 + v_const * (t34 - t_start_4);

% 阶段5: 加速度减少(开始减速)
t_start_5 = t_start_4 + t_v;
t45 = (t_start_5+dt):dt:(t_start_5+t_j3);
if isempty(t45); t45 = t_start_5; end
jerk45 = -J * ones(size(t45));
a45 = -J * (t45 - t_start_5);
v45 = v_const - 0.5 * J * (t45 - t_start_5).^2;
s_start_5 = s_start_4 + v_const * t_v;
s45 = s_start_5 + v_const * (t45 - t_start_5) - (1/6) * J * (t45 - t_start_5).^3;

% 阶段6: 恒定减速度
t_start_6 = t_start_5 + t_j3;
t56 = (t_start_6+dt):dt:(t_start_6+t_d);
if isempty(t56); t56 = t_start_6; end
jerk56 = zeros(size(t56));
a56 = a_min * ones(size(t56));
v_start_6 = v_const - 0.5 * J * t_j3^2;
v56 = v_start_6 + a_min * (t56 - t_start_6);
s_start_6 = s_start_5 + v_const * t_j3 - (1/6) * J * t_j3^3;
s56 = s_start_6 + v_start_6 * (t56 - t_start_6) + 0.5 * a_min * (t56 - t_start_6).^2;

% 阶段7: 加速度增加(减速结束)
t_start_7 = t_start_6 + t_d;
t67 = (t_start_7+dt):dt:(t_start_7+t_j4);
if isempty(t67); t67 = t_start_7; end
jerk67 = J * ones(size(t67));
a67 = a_min + J * (t67 - t_start_7);
v_start_7 = v_start_6 + a_min * t_d;
v67 = v_start_7 + a_min * (t67 - t_start_7) + 0.5 * J * (t67 - t_start_7).^2;
s_start_7 = s_start_6 + v_start_6 * t_d + 0.5 * a_min * t_d^2;
s67 = s_start_7 + v_start_7 * (t67 - t_start_7) + 0.5 * a_min * (t67 - t_start_7).^2 + (1/6) * J * (t67 - t_start_7).^3;

% 输出总体参数
T_total = t67(end);
V_max = v_const;
A_max = a_max;
J_max = J;

% 输出阶段信息
phase_times = [t_j1, t_a, t_j2, t_v, t_j3, t_d, t_j4];
phase_positions = [s01(end), s12(end), s23(end), s34(end), s45(end), s56(end), s67(end)];
phase_velocities = [v01(end), v12(end), v23(end), v34(end), v45(end), v56(end), 0];

end
