%% 修正版S曲线生成器测试
% 基于正确的物理公式重新实现

clear; clc; close all;

fprintf('=== 修正版S曲线生成器测试 ===\n');

% 项目标准参数
position_set = 4000.0;      % 目标位置 (mm)
velocity_set = 600.0;       % 最大速度 (mm/s)
positive_accel_set = 300.0; % 正向加速度 (mm/s²)
negative_accel_set = 800.0; % 负向加速度 (mm/s²)
jerk_set = 600.0;           % 加加速度 (mm/s³)

fprintf('输入参数:\n');
fprintf('  目标位置: %.0f mm\n', position_set);
fprintf('  最大速度: %.0f mm/s\n', velocity_set);
fprintf('  正向加速度: %.0f mm/s²\n', positive_accel_set);
fprintf('  负向加速度: %.0f mm/s²\n', negative_accel_set);
fprintf('  加加速度: %.0f mm/s³\n', jerk_set);

try
    % 调用修正版S曲线生成器
    [time_vec, pos_vec, vel_vec, acc_vec] = generate_corrected_s_curve(...
        position_set, velocity_set, positive_accel_set, negative_accel_set, jerk_set);
    
    fprintf('\n结果:\n');
    fprintf('  总时长: %.3f s\n', time_vec(end));
    fprintf('  数据点数: %d\n', length(time_vec));
    fprintf('  最终位置: %.6f mm\n', pos_vec(end));
    fprintf('  位置误差: %.6f mm\n', abs(pos_vec(end) - position_set));
    fprintf('  最终速度: %.6f mm/s\n', vel_vec(end));
    fprintf('  最大速度: %.3f mm/s\n', max(vel_vec));
    
    % 验证精度
    position_error = abs(pos_vec(end) - position_set);
    velocity_error = abs(vel_vec(end));
    
    if position_error < 0.1 && velocity_error < 0.001
        fprintf('  ✅ 测试通过! 精度满足要求\n');
        
        % 绘制结果
        figure('Name', '修正版S曲线测试结果', 'Position', [100, 100, 1000, 600]);
        
        subplot(3,1,1);
        plot(time_vec, pos_vec, 'b-', 'LineWidth', 2);
        title('位置轨迹');
        xlabel('时间 (s)'); ylabel('位置 (mm)');
        grid on;
        
        subplot(3,1,2);
        plot(time_vec, vel_vec, 'r-', 'LineWidth', 2);
        title('速度轨迹');
        xlabel('时间 (s)'); ylabel('速度 (mm/s)');
        grid on;
        
        subplot(3,1,3);
        plot(time_vec, acc_vec, 'g-', 'LineWidth', 2);
        title('加速度轨迹');
        xlabel('时间 (s)'); ylabel('加速度 (mm/s²)');
        grid on;
        
        sgtitle('修正版S曲线生成器测试结果', 'FontSize', 14);
        
    else
        fprintf('  ❌ 测试失败! 精度不满足要求\n');
    end
    
catch ME
    fprintf('  ❌ 错误: %s\n', ME.message);
end

function [time_vec, pos_vec, vel_vec, acc_vec] = generate_corrected_s_curve(s_target, v_max, a_accel, a_decel, j_max)
%% 修正版S曲线生成器
% 基于正确的物理公式和数值积分

Ts = 0.004; % 采样时间

% 参数验证
if s_target <= 0 || v_max <= 0 || a_accel <= 0 || a_decel <= 0 || j_max <= 0
    error('所有参数必须为正数');
end

% Step 1: 计算时间参数
% 加速段
t_j1 = a_accel / j_max;  % 达到最大加速度的时间
v_j1 = 0.5 * a_accel * t_j1;  % 达到最大加速度时的速度

% 减速段
t_j2 = a_decel / j_max;  % 达到最大减速度的时间
v_j2 = 0.5 * a_decel * t_j2;  % 减速段开始时需要的速度

% 检查是否能达到最大速度
if v_j1 + v_j2 <= v_max
    % 能达到最大速度 - 梯形轮廓
    t_a = (v_max - v_j1) / a_accel;  % 恒加速时间
    t_d = (v_max - v_j2) / a_decel;  % 恒减速时间
    v_reach = v_max;
else
    % 不能达到最大速度 - 三角形轮廓
    % 重新计算时间参数
    v_reach = (s_target * j_max + 0.5 * (a_accel + a_decel) * (a_accel/j_max + a_decel/j_max)) / ...
              (a_accel/j_max + a_decel/j_max + (a_accel + a_decel)/j_max);
    
    if v_reach > v_max
        v_reach = v_max;
        t_a = (v_max - v_j1) / a_accel;
        t_d = (v_max - v_j2) / a_decel;
    else
        t_j1 = sqrt(v_reach / j_max);
        t_j2 = t_j1;
        a_accel = j_max * t_j1;
        a_decel = a_accel;
        t_a = 0;
        t_d = 0;
        v_j1 = 0.5 * a_accel * t_j1;
        v_j2 = v_j1;
    end
end

% Step 2: 计算距离
% 加速段距离
s_j1 = (1/6) * j_max * t_j1^3;  % 加加速段
s_a = v_j1 * t_a + 0.5 * a_accel * t_a^2;  % 恒加速段
s_accel_total = 2 * s_j1 + s_a;

% 减速段距离
s_j2 = (1/6) * j_max * t_j2^3;  % 减减速段
s_d = v_j2 * t_d + 0.5 * a_decel * t_d^2;  % 恒减速段
s_decel_total = 2 * s_j2 + s_d;

% 恒速段
s_const = s_target - s_accel_total - s_decel_total;
t_v = max(0, s_const / v_reach);

% Step 3: 计算时间节点
T1 = t_j1;                    % 加速度增加结束
T2 = T1 + t_a;               % 恒加速结束
T3 = T2 + t_j1;              % 加速度减少结束
T4 = T3 + t_v;               % 恒速结束
T5 = T4 + t_j2;              % 减速度增加结束
T6 = T5 + t_d;               % 恒减速结束
T7 = T6 + t_j2;              % 减速度减少结束

% Step 4: 生成轨迹
time_vec = (0:Ts:T7)';
N = length(time_vec);

pos_vec = zeros(N, 1);
vel_vec = zeros(N, 1);
acc_vec = zeros(N, 1);

% 数值积分生成轨迹
for i = 2:N
    t = time_vec(i-1);
    
    % 确定当前阶段的加加速度
    if t < T1
        jerk = j_max;                    % 阶段1：加速度增加
    elseif t < T2
        jerk = 0;                        % 阶段2：恒加速
    elseif t < T3
        jerk = -j_max;                   % 阶段3：加速度减少
    elseif t < T4
        jerk = 0;                        % 阶段4：恒速
    elseif t < T5
        jerk = -j_max;                   % 阶段5：减速度增加
    elseif t < T6
        jerk = 0;                        % 阶段6：恒减速
    elseif t < T7
        jerk = j_max;                    % 阶段7：减速度减少
    else
        jerk = 0;                        % 结束
    end
    
    % 数值积分
    acc_vec(i) = acc_vec(i-1) + jerk * Ts;
    
    % 限制加速度
    if acc_vec(i) > a_accel
        acc_vec(i) = a_accel;
    elseif acc_vec(i) < -a_decel
        acc_vec(i) = -a_decel;
    end
    
    % 更新速度和位置
    vel_vec(i) = vel_vec(i-1) + acc_vec(i-1) * Ts;
    pos_vec(i) = pos_vec(i-1) + vel_vec(i-1) * Ts + 0.5 * acc_vec(i-1) * Ts^2;
end

% 确保最终状态
vel_vec(end) = 0;
acc_vec(end) = 0;

% 位置校正（如果需要）
final_error = pos_vec(end) - s_target;
if abs(final_error) > 0.01
    % 线性校正最后一段
    correction_start = max(1, round(0.9 * N));
    for i = correction_start:N
        ratio = (i - correction_start + 1) / (N - correction_start + 1);
        pos_vec(i) = pos_vec(i) - final_error * ratio;
    end
end

end
