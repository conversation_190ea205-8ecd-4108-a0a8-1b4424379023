# 走架细纱机牵伸控制算法使用指南

## 快速开始

### 1. 环境要求
- MATLAB R2018b或更高版本
- 支持基本的数值计算和绘图功能
- 建议配置：8GB内存，多核处理器

### 2. 文件结构
```
牵伸优化/
├── simplified_test.m           # 主要算法文件（推荐使用）
├── corrected_s_curve.m        # S曲线生成器测试
├── brake.m                     # 刹车距离计算器
├── poscontrol_calculation.m    # 原始S曲线函数（参考）
├── 技术文档/
│   ├── 走架细纱机牵伸控制算法技术报告.md
│   └── 算法使用指南.md
└── 测试文件/
    ├── test_s_curve_golden.m
    ├── simple_s_test.m
    └── final_integrated_algorithm.m
```

### 3. 快速运行
```matlab
% 在MATLAB命令窗口中运行
run('simplified_test.m')
```

## 参数配置

### 工艺参数设置

在`simplified_test.m`文件中修改以下参数：

```matlab
%% 工艺参数配置
stroke = 4000.0;           % 走车行程 (mm)
max_speed = 600.0;         % 走车最大速度 (mm/s)
accel_pos = 300.0;         % 走车正向加速度 (mm/s²)
accel_neg = 800.0;         % 走车负向加速度 (mm/s²)
jerk = 600.0;              % 走车加加速度 (mm/s³)

luola_accel = 2000.0;      % 罗拉刹车加速度 (mm/s²)
luola_jerk = 12500.0;      % 罗拉刹车加加速度 (mm/s³)

ratio_distributed = 1.2;   % 分散牵伸比
ratio_total = 1.5;         % 总牵伸比

Ts = 0.004;                % 采样时间 (s)
```

### 参数调整建议

#### 走车参数
- **行程 (stroke)**：根据实际设备设定，通常2000-6000mm
- **最大速度 (max_speed)**：根据工艺要求，建议300-800mm/s
- **加速度 (accel_pos/neg)**：影响启停平稳性，建议200-500mm/s²
- **加加速度 (jerk)**：影响轨迹平滑性，建议400-1000mm/s³

#### 罗拉参数
- **刹车加速度 (luola_accel)**：关键参数，影响总牵伸比精度
  - 过小：无法及时减速，总牵伸比偏大
  - 过大：减速过快，可能产生冲击
  - 建议范围：1500-3000mm/s²

- **刹车加加速度 (luola_jerk)**：影响减速平滑性
  - 建议为走车加加速度的10-20倍
  - 范围：8000-15000mm/s³

#### 牵伸比参数
- **分散牵伸比 (ratio_distributed)**：第一段牵伸比，通常1.1-1.3
- **总牵伸比 (ratio_total)**：最终牵伸比，通常1.3-2.0
- **约束条件**：ratio_total > ratio_distributed

## 功能模块说明

### 1. S曲线轨迹生成器

```matlab
[time, pos, vel] = generate_simple_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
```

**输入参数：**
- `dist`: 目标距离 (mm)
- `v_max`: 最大速度 (mm/s)
- `a_accel`: 加速度 (mm/s²)
- `a_decel`: 减速度 (mm/s²)
- `j_max`: 加加速度 (mm/s³)
- `Ts`: 采样时间 (s)

**输出参数：**
- `time`: 时间序列 (s)
- `pos`: 位置序列 (mm)
- `vel`: 速度序列 (mm/s)

### 2. 刹车距离计算器

```matlab
brake_dist = calculate_simple_brake_distance(v0, a_decel, j_max)
```

**输入参数：**
- `v0`: 初始速度 (mm/s)
- `a_decel`: 减速度 (mm/s²)
- `j_max`: 加加速度 (mm/s³)

**输出参数：**
- `brake_dist`: 刹车距离 (mm)

### 3. 减速轨迹生成器

```matlab
[time, vel] = generate_simple_decel(v0, a_decel, j_max, Ts)
```

**输入参数：**
- `v0`: 初始速度 (mm/s)
- `a_decel`: 减速度 (mm/s²)
- `j_max`: 加加速度 (mm/s³)
- `Ts`: 采样时间 (s)

**输出参数：**
- `time`: 时间序列 (s)
- `vel`: 速度序列 (mm/s)

## 结果分析

### 1. 关键输出指标

运行算法后，重点关注以下输出：

```
实际总牵伸比: X.XXXXXX (目标: 1.5)
实际分散牵伸比: X.XXXXXX (目标: 1.2)
罗拉最终位置: XXXX.XXX mm (目标: XXXX.XXX mm)
位置误差: X.XXXXXX mm
```

### 2. 验收标准

- **总牵伸比误差** < 0.01 (1%)
- **分散牵伸比误差** < 0.01 (1%)
- **位置误差** < 1.0 mm
- **拐点误差** < 0.1 mm

### 3. 仿真图表解读

算法会生成4个子图：

1. **位置轨迹对比**：显示走车和罗拉的位置变化
2. **速度轨迹对比**：显示走车和罗拉的速度变化
3. **实时牵伸比变化**：显示牵伸比的实时变化过程
4. **关键参数**：显示重要的数值结果

## 故障排除

### 常见问题及解决方案

#### 1. 总牵伸比误差过大
**现象**：实际总牵伸比与目标值差异超过1%

**可能原因**：
- 罗拉刹车加速度设置不当
- 拐点搜索精度不够
- S曲线生成器参数不匹配

**解决方案**：
```matlab
% 调整罗拉刹车加速度
luola_accel = 2500.0;  % 增加刹车加速度

% 或者调整拐点搜索精度
if error_val < 0.05    % 降低容差要求
```

#### 2. 位置误差过大
**现象**：罗拉最终位置误差超过1mm

**可能原因**：
- 刹车距离计算不准确
- 数值积分误差累积
- 参数设置不合理

**解决方案**：
```matlab
% 减小采样时间提高精度
Ts = 0.002;  % 从0.004减小到0.002

% 或者调整刹车参数
luola_jerk = 15000.0;  % 增加刹车加加速度
```

#### 3. 算法运行失败
**现象**：MATLAB报错或无输出

**检查项目**：
- 确认所有参数为正数
- 检查文件路径是否正确
- 验证MATLAB版本兼容性

**解决方案**：
```matlab
% 参数验证
if stroke <= 0 || max_speed <= 0
    error('参数必须为正数');
end

% 路径检查
if ~exist('simplified_test.m', 'file')
    error('找不到算法文件');
end
```

## 性能优化

### 1. 计算效率优化

```matlab
% 减少循环次数
step_size = 10;  % 每10个点检查一次拐点
for i = N:-step_size:1
    % 拐点搜索逻辑
end

% 预分配数组
pos = zeros(N, 1);
vel = zeros(N, 1);
```

### 2. 精度与速度平衡

```matlab
% 高精度模式（慢）
Ts = 0.001;

% 标准模式（推荐）
Ts = 0.004;

% 快速模式（低精度）
Ts = 0.01;
```

## 工程应用建议

### 1. 参数调试流程

1. **基础验证**：使用默认参数运行算法
2. **参数调整**：根据实际工艺要求调整参数
3. **精度验证**：检查关键指标是否满足要求
4. **稳定性测试**：多次运行验证结果一致性
5. **边界测试**：测试极限参数下的算法表现

### 2. 集成注意事项

- 确保控制器采样时间与算法一致
- 添加实时监控和异常处理
- 实现参数在线调整功能
- 保留调试和诊断接口

### 3. 维护建议

- 定期备份参数配置
- 记录参数调整历史
- 建立故障诊断数据库
- 制定标准操作程序

---

**技术支持**：如遇到问题，请参考技术报告或联系开发团队  
**版本信息**：V1.0 - 2024年7月  
**更新日志**：初始版本，包含核心功能和基础文档
