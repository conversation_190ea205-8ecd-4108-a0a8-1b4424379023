%% 简化版牵伸控制算法测试
clear; clc; close all;

fprintf('=== 简化版牵伸控制算法测试 ===\n');

% 工艺参数
stroke = 4000.0;           % 走车行程 (mm)
max_speed = 600.0;         % 走车最大速度 (mm/s)
accel_pos = 300.0;         % 走车正向加速度 (mm/s²)
accel_neg = 800.0;         % 走车负向加速度 (mm/s²)
jerk = 600.0;              % 走车加加速度 (mm/s³)

luola_accel = 2000.0;      % 罗拉刹车加速度 (mm/s²)
luola_jerk = 12500.0;      % 罗拉刹车加加速度 (mm/s³)

ratio_distributed = 1.2;   % 分散牵伸比
ratio_total = 1.5;         % 总牵伸比

Ts = 0.004;                % 采样时间

fprintf('参数配置:\n');
fprintf('  走车行程: %.0f mm\n', stroke);
fprintf('  分散牵伸比: %.1f\n', ratio_distributed);
fprintf('  总牵伸比: %.1f\n', ratio_total);

%% 第一步：生成走车轨迹
fprintf('\n第一步：生成走车轨迹...\n');

try
    [master_time, master_pos, master_vel] = generate_simple_s_curve(stroke, max_speed, accel_pos, accel_neg, jerk, Ts);
    
    fprintf('  ✅ 走车轨迹生成成功\n');
    fprintf('  总时长: %.3f s\n', master_time(end));
    fprintf('  最终位置: %.6f mm\n', master_pos(end));
    fprintf('  位置误差: %.6f mm\n', abs(master_pos(end) - stroke));
    
catch ME
    fprintf('  ❌ 走车轨迹生成失败: %s\n', ME.message);
    return;
end

%% 第二步：计算拐点
fprintf('\n第二步：计算去同步拐点...\n');

% 理想同步轨迹
ideal_slave_pos = master_pos / ratio_distributed;
ideal_slave_vel = master_vel / ratio_distributed;

% 目标位置
target_pos = stroke / ratio_total;
fprintf('  罗拉目标位置: %.3f mm\n', target_pos);

% 寻找拐点
best_error = inf;
best_index = 1;
N = length(master_time);

for i = N:-1:1
    current_pos = ideal_slave_pos(i);
    current_vel = ideal_slave_vel(i);
    
    % 计算刹车距离
    brake_dist = calculate_simple_brake_distance(current_vel, luola_accel, luola_jerk);
    
    % 预期停止位置
    expected_stop = current_pos + brake_dist;
    
    % 计算误差
    error_val = abs(expected_stop - target_pos);
    
    if error_val < best_error
        best_error = error_val;
        best_index = i;
    end
    
    if error_val < 0.1
        break;
    end
end

turning_point_index = best_index;
turning_point_time = master_time(turning_point_index);
turning_point_pos = ideal_slave_pos(turning_point_index);
turning_point_vel = ideal_slave_vel(turning_point_index);

fprintf('  ✅ 拐点计算完成\n');
fprintf('  拐点位置: %.3f mm\n', turning_point_pos);
fprintf('  拐点时刻: %.3f s\n', turning_point_time);
fprintf('  拐点速度: %.3f mm/s\n', turning_point_vel);
fprintf('  拐点误差: %.6f mm\n', best_error);

%% 第三步：生成罗拉轨迹
fprintf('\n第三步：生成罗拉完整轨迹...\n');

% 同步段
sync_time = master_time(1:turning_point_index);
sync_pos = ideal_slave_pos(1:turning_point_index);
sync_vel = ideal_slave_vel(1:turning_point_index);

% 减速段
[decel_time_rel, decel_vel] = generate_simple_decel(turning_point_vel, luola_accel, luola_jerk, Ts);
decel_time = decel_time_rel + turning_point_time;

% 计算减速段位置
decel_pos = zeros(size(decel_vel));
decel_pos(1) = turning_point_pos;
for i = 2:length(decel_pos)
    decel_pos(i) = decel_pos(i-1) + (decel_vel(i-1) + decel_vel(i)) * 0.5 * Ts;
end

% 拼接完整轨迹
complete_time = [sync_time; decel_time(2:end)];
complete_pos = [sync_pos; decel_pos(2:end)];
complete_vel = [sync_vel; decel_vel(2:end)];

fprintf('  ✅ 罗拉轨迹生成完成\n');
fprintf('  同步段时长: %.3f s\n', sync_time(end));
fprintf('  减速段时长: %.3f s\n', decel_time(end) - decel_time(1));

%% 第四步：验证结果
fprintf('\n第四步：验证工艺质量...\n');

actual_total_ratio = master_pos(end) / complete_pos(end);
actual_distributed_ratio = master_pos(turning_point_index) / complete_pos(turning_point_index);
final_pos_error = abs(complete_pos(end) - target_pos);

fprintf('  实际总牵伸比: %.6f (目标: %.1f)\n', actual_total_ratio, ratio_total);
fprintf('  实际分散牵伸比: %.6f (目标: %.1f)\n', actual_distributed_ratio, ratio_distributed);
fprintf('  罗拉最终位置: %.3f mm (目标: %.3f mm)\n', complete_pos(end), target_pos);
fprintf('  位置误差: %.6f mm\n', final_pos_error);

% 验收判断
ratio_ok = abs(actual_total_ratio - ratio_total) < 0.01;
distributed_ok = abs(actual_distributed_ratio - ratio_distributed) < 0.01;
position_ok = final_pos_error < 1.0;

if ratio_ok && distributed_ok && position_ok
    fprintf('  ✅ 工艺验收: 通过\n');
else
    fprintf('  ⚠️  工艺验收: 需要优化\n');
end

%% 第五步：绘制结果
fprintf('\n第五步：生成仿真图表...\n');

figure('Name', '简化版牵伸控制算法仿真结果', 'Position', [100, 100, 1200, 800]);

% 位置对比
subplot(2,2,1);
plot(master_time, master_pos, 'b-', 'LineWidth', 2);
hold on;
plot(complete_time, complete_pos, 'r-', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [0, max(master_pos)], 'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('位置轨迹对比');
xlabel('时间 (s)'); ylabel('位置 (mm)');
legend('走车', '罗拉', '拐点', 'Location', 'best');
grid on;

% 速度对比
subplot(2,2,2);
plot(master_time, master_vel, 'b-', 'LineWidth', 2);
hold on;
plot(complete_time, complete_vel, 'r-', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [0, max(master_vel)], 'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('速度轨迹对比');
xlabel('时间 (s)'); ylabel('速度 (mm/s)');
legend('走车', '罗拉', '拐点', 'Location', 'best');
grid on;

% 实时牵伸比
subplot(2,2,3);
master_pos_interp = interp1(master_time, master_pos, complete_time, 'linear', 'extrap');
realtime_ratio = master_pos_interp ./ complete_pos;
plot(complete_time, realtime_ratio, 'k-', 'LineWidth', 2);
hold on;
line([0, complete_time(end)], [ratio_distributed, ratio_distributed], 'Color', 'r', 'LineStyle', '--', 'LineWidth', 2);
line([0, complete_time(end)], [ratio_total, ratio_total], 'Color', 'b', 'LineStyle', '--', 'LineWidth', 2);
line([turning_point_time, turning_point_time], [1, 2], 'Color', 'g', 'LineStyle', '--', 'LineWidth', 2);
title('实时牵伸比变化');
xlabel('时间 (s)'); ylabel('牵伸比');
legend('实时比', '分散牵伸比', '总牵伸比', '拐点', 'Location', 'best');
grid on;

% 关键参数
subplot(2,2,4);
param_text = {
    ['走车行程: ' num2str(stroke) ' mm']
    ['罗拉行程: ' num2str(complete_pos(end), '%.1f') ' mm']
    ['实际总比: ' num2str(actual_total_ratio, '%.6f')]
    ['实际分散比: ' num2str(actual_distributed_ratio, '%.6f')]
    ['位置误差: ' num2str(final_pos_error, '%.3f') ' mm']
    ['拐点位置: ' num2str(turning_point_pos, '%.1f') ' mm']
    ['拐点时刻: ' num2str(turning_point_time, '%.3f') ' s']
};
text(0.1, 0.5, param_text, 'FontSize', 10, 'VerticalAlignment', 'middle');
axis off; title('关键参数');

sgtitle('简化版牵伸控制算法仿真结果', 'FontSize', 14, 'FontWeight', 'bold');

fprintf('  ✅ 仿真图表生成完成\n');

fprintf('\n=== 测试总结 ===\n');
if ratio_ok && distributed_ok && position_ok
    fprintf('🎉 算法验证成功!\n');
    fprintf('✅ 核心功能正常工作\n');
else
    fprintf('⚠️  算法需要进一步优化\n');
end

%% 辅助函数

function [time, pos, vel] = generate_simple_s_curve(dist, v_max, a_accel, a_decel, j_max, Ts)
%% 简化S曲线生成器

% 时间参数
t_j1 = a_accel / j_max;
t_j2 = a_decel / j_max;

% 检查速度限制
v_j1 = 0.5 * a_accel * t_j1;
v_j2 = 0.5 * a_decel * t_j2;

if v_j1 + v_j2 <= v_max
    t_a = (v_max - v_j1) / a_accel;
    t_d = (v_max - v_j2) / a_decel;
    v_reach = v_max;
else
    v_reach = sqrt(dist * j_max / 2);
    t_j1 = sqrt(v_reach / j_max);
    t_j2 = t_j1;
    t_a = 0;
    t_d = 0;
end

% 距离计算
s_accel = (1/3) * j_max * t_j1^3 + v_j1 * t_a;
s_decel = (1/3) * j_max * t_j2^3 + v_j2 * t_d;
s_const = dist - s_accel - s_decel;
t_v = max(0, s_const / v_reach);

% 时间节点
T1 = t_j1;
T2 = T1 + t_a;
T3 = T2 + t_j1;
T4 = T3 + t_v;
T5 = T4 + t_j2;
T6 = T5 + t_d;
T7 = T6 + t_j2;

% 生成轨迹
time = (0:Ts:T7)';
N = length(time);
pos = zeros(N, 1);
vel = zeros(N, 1);
acc = zeros(N, 1);

for i = 2:N
    t = time(i-1);
    
    if t < T1
        jerk = j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = -j_max;
    elseif t < T4
        jerk = 0;
    elseif t < T5
        jerk = -j_max;
    elseif t < T6
        jerk = 0;
    elseif t < T7
        jerk = j_max;
    else
        jerk = 0;
    end
    
    acc(i) = acc(i-1) + jerk * Ts;
    vel(i) = vel(i-1) + acc(i-1) * Ts;
    pos(i) = pos(i-1) + vel(i-1) * Ts + 0.5 * acc(i-1) * Ts^2;
end

vel(end) = 0;
end

function brake_dist = calculate_simple_brake_distance(v0, a_decel, j_max)
%% 简化刹车距离计算
if v0 < 0.001
    brake_dist = 0;
    return;
end

t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    t_j = sqrt(v0/j_max);
    t_const = 0;
else
    t_const = v0/a_decel - t_j;
end

% 简化距离公式
brake_dist = v0^2 / (2 * a_decel) + (a_decel * t_j^2) / 6;
end

function [time, vel] = generate_simple_decel(v0, a_decel, j_max, Ts)
%% 简化减速轨迹生成器
if v0 < 0.001
    time = 0;
    vel = 0;
    return;
end

t_j = a_decel / j_max;
if v0 * j_max < a_decel^2
    t_j = sqrt(v0/j_max);
    t_const = 0;
    T_total = 2 * t_j;
else
    t_const = v0/a_decel - t_j;
    T_total = 2 * t_j + t_const;
end

T1 = t_j;
T2 = T1 + t_const;
T3 = T2 + t_j;

time = (0:Ts:T_total)';
N = length(time);
vel = zeros(N,1);
acc = zeros(N,1);
vel(1) = v0;

for i = 2:N
    t = time(i-1);
    
    if t < T1
        jerk = -j_max;
    elseif t < T2
        jerk = 0;
    elseif t < T3
        jerk = j_max;
    else
        jerk = 0;
    end
    
    acc(i) = acc(i-1) + jerk * Ts;
    if acc(i) < -a_decel
        acc(i) = -a_decel;
    end
    
    vel(i) = vel(i-1) + acc(i-1) * Ts;
    if vel(i) < 0
        vel(i) = 0;
    end
end

vel(end) = 0;
end
